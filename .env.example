# Django Google Authentication Environment Variables
# Copy this file to .env and fill in your actual values

# Django Settings
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=127.0.0.1,localhost

# Google OAuth Credentials
# Get these from Google Cloud Console
GOOGLE_CLIENT_ID=232409669201-0h2h8pnmch8t6bkb1cbbi4vtdljfht1s.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# Database (optional - defaults to SQLite)
# DATABASE_URL=sqlite:///db.sqlite3

# Email Configuration (optional)
# EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
# EMAIL_HOST=smtp.gmail.com
# EMAIL_PORT=587
# EMAIL_USE_TLS=True
# EMAIL_HOST_USER=<EMAIL>
# EMAIL_HOST_PASSWORD=your-app-password
