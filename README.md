# Django Google Authentication Project

This project demonstrates how to integrate Google Sign-In with Django using django-allauth.

## Features

- Google OAuth2 authentication
- User registration and login
- Protected views based on authentication status
- Clean, responsive UI
- User profile information display

## Prerequisites

- Python 3.8+
- Django 5.1+
- Google Cloud Platform account

## Installation

1. **Clone the repository:**
   ```bash
   git clone <your-repo-url>
   cd django-google-auth
   ```

2. **Create and activate virtual environment:**
   ```bash
   python -m venv myenv
   # On Windows:
   myenv\Scripts\activate
   # On macOS/Linux:
   source myenv/bin/activate
   ```

3. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

## Google Cloud Platform Setup

### Step 1: Create a Google Cloud Project

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Note your project ID

### Step 2: Configure OAuth Consent Screen

1. Navigate to **APIs & Services > OAuth consent screen**
2. Choose **External** user type and click **Create**
3. Fill in the required fields:
   - **App name**: Your application name
   - **User support email**: Your email address
   - **Developer contact information**: Your email address
4. Click **Save and Continue** for "Scopes" and "Test users" sections

### Step 3: Create OAuth Client ID

1. Go to **APIs & Services > Credentials**
2. Click **Create Credentials > OAuth client ID**
3. Select **Web application** as the application type
4. Under **Authorized redirect URIs**, add:
   - `http://127.0.0.1:8000/accounts/google/login/callback/`
   - `http://localhost:8000/accounts/google/login/callback/`
5. Click **Create**
6. Copy the **Client ID** and **Client Secret**

## Django Configuration

### Update Google Credentials

1. Open `django_google_auth/settings.py`
2. Find the `SOCIALACCOUNT_PROVIDERS` section
3. Replace the placeholder values:
   ```python
   SOCIALACCOUNT_PROVIDERS = {
       'google': {
           'SCOPE': [
               'profile',
               'email',
           ],
           'AUTH_PARAMS': {
               'access_type': 'online',
           },
           'APP': {
               'client_id': 'YOUR_ACTUAL_GOOGLE_CLIENT_ID',
               'secret': 'YOUR_ACTUAL_GOOGLE_CLIENT_SECRET',
           }
       }
   }
   ```

### Run Migrations

```bash
python manage.py migrate
```

### Create Superuser (Optional)

```bash
python manage.py createsuperuser
```

## Running the Application

1. **Start the development server:**
   ```bash
   python manage.py runserver
   ```

2. **Open your browser and navigate to:**
   ```
   http://127.0.0.1:8000/
   ```

3. **Test the authentication:**
   - Click "Login" to access the login page
   - Click "Login with Google" to authenticate with Google
   - After successful authentication, you'll be redirected to the home page

## Project Structure

```
django-google-auth/
├── django_google_auth/          # Main project directory
│   ├── __init__.py
│   ├── settings.py              # Django settings with allauth config
│   ├── urls.py                  # Main URL configuration
│   └── wsgi.py
├── main/                        # Main application
│   ├── __init__.py
│   ├── views.py                 # Home view
│   ├── urls.py                  # App URL patterns
│   └── ...
├── templates/                   # Template directory
│   ├── base.html               # Base template
│   ├── home.html               # Home page template
│   └── account/
│       └── login.html          # Custom login template
├── manage.py
├── requirements.txt
└── README.md
```

## Key Files

- **settings.py**: Contains django-allauth configuration and Google OAuth settings
- **templates/home.html**: Displays user information when authenticated
- **templates/account/login.html**: Custom login page with Google Sign-In button
- **main/views.py**: Contains the home view logic

## Troubleshooting

### Common Issues

1. **"Invalid redirect URI" error:**
   - Ensure the redirect URIs in Google Cloud Console match exactly
   - Check for trailing slashes and correct ports

2. **"Client ID not found" error:**
   - Verify your Client ID and Secret are correctly set in settings.py
   - Ensure there are no extra spaces or quotes

3. **Template not found errors:**
   - Check that `DIRS` in `TEMPLATES` setting includes the templates directory
   - Verify template file paths are correct

### Debug Mode

The project runs in DEBUG mode by default. For production:
1. Set `DEBUG = False` in settings.py
2. Configure `ALLOWED_HOSTS`
3. Use environment variables for sensitive data

## Security Notes

- Never commit your actual Google Client ID and Secret to version control
- Use environment variables for production credentials
- Consider using django-environ for environment variable management

## Next Steps

- Add user profile management
- Implement additional OAuth providers
- Add email verification
- Configure production deployment settings
