{% extends "account/base_entrance.html" %}
{% load i18n %}
{% load static %}
{% load allauth %}
{% block content %}
    {% element h1 %}
        {% trans "Create Passkey" %}
    {% endelement %}
    {% element p %}
        {% blocktranslate %}You are about to create a passkey for your account. As you can add additional keys later on, you can use a descriptive name to tell the keys apart.{% endblocktranslate %}
    {% endelement %}
    {% url 'mfa_signup_webauthn' as action_url %}
    {% element form form=form method="post" action=action_url %}
        {% slot body %}
            {% csrf_token %}
            {% element fields form=form %}
            {% endelement %}
        {% endslot %}
        {% slot actions %}
            {% element button id="mfa_webauthn_signup" type="button" %}
                {% trans "Create" %}
            {% endelement %}
        {% endslot %}
    {% endelement %}
    {% element button type="submit" form="logout-from-stage" tags="link,cancel" %}
        {% translate "Cancel" %}
    {% endelement %}
    <form id="logout-from-stage"
          method="post"
          action="{% url 'account_logout' %}">
        <input type="hidden" name="next" value="{% url 'account_login' %}">
        {% csrf_token %}
    </form>
    {% include "mfa/webauthn/snippets/scripts.html" %}
    {{ js_data|json_script:"js_data" }}
    <script data-allauth-onload="allauth.webauthn.forms.signupForm" type="application/json">{
    "ids": {
        "signup": "mfa_webauthn_signup",
        "credential": "{{ form.credential.auto_id }}",
        "data": "js_data"
    }
}
    </script>
{% endblock %}
