#!/usr/bin/env python3
"""
Setup script for Django Google Authentication project.
This script helps automate the initial setup process.
"""

import os
import sys
import subprocess
import django
from django.core.management import execute_from_command_line

def run_command(command, description):
    """Run a shell command and handle errors."""
    print(f"\n{description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✓ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Error during {description.lower()}: {e}")
        print(f"Error output: {e.stderr}")
        return False

def setup_project():
    """Main setup function."""
    print("🚀 Setting up Django Google Authentication project...")
    
    # Check if virtual environment is activated
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("⚠️  Warning: Virtual environment not detected.")
        print("It's recommended to run this in a virtual environment.")
        response = input("Continue anyway? (y/N): ")
        if response.lower() != 'y':
            print("Setup cancelled.")
            return False
    
    # Install dependencies
    if not run_command("pip install -r requirements.txt", "Installing dependencies"):
        return False
    
    # Set up Django environment
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'django_google_auth.settings')
    django.setup()
    
    # Run migrations
    print("\n🔄 Running database migrations...")
    try:
        execute_from_command_line(['manage.py', 'migrate'])
        print("✓ Database migrations completed successfully")
    except Exception as e:
        print(f"✗ Error running migrations: {e}")
        return False
    
    # Create superuser (optional)
    print("\n👤 Would you like to create a superuser account?")
    response = input("This is optional but useful for accessing the admin panel (y/N): ")
    if response.lower() == 'y':
        try:
            execute_from_command_line(['manage.py', 'createsuperuser'])
        except KeyboardInterrupt:
            print("\nSuperuser creation cancelled.")
    
    print("\n✅ Setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Set up Google OAuth credentials in Google Cloud Console")
    print("2. Update the CLIENT_ID and CLIENT_SECRET in settings.py")
    print("3. Run 'python manage.py runserver' to start the development server")
    print("4. Visit http://127.0.0.1:8000/ to test the application")
    print("\n📖 See README.md for detailed instructions on Google Cloud setup.")
    
    return True

if __name__ == "__main__":
    success = setup_project()
    sys.exit(0 if success else 1)
